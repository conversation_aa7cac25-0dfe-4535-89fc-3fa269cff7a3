<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <title>测试报告</title>
  <meta name="generator" content="XTestRunner 1.8.4" />
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <script src="https://libs.baidu.com/jquery/2.0.0/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.2/echarts.min.js"></script>
  <link rel="stylesheet" href="https://telegraph-image-cq2.pages.dev/seldom-main.css" />
  <link rel="stylesheet" href="https://telegraph-image-cq2.pages.dev/seldom-utilities.css" />
  <link rel="icon" href="https://telegraph-image-cq2.pages.dev/XTestRunnerIcon.png" type="image/x-icon" />
  <style type="text/css" media="screen">
    body {
      font-family: verdana, arial, helvetica, sans-serif;
      font-size: 80%;
    }

    table {
      font-size: 100%;
    }

    .table td {
      white-space: inherit !important;
    }

    /* -- heading ---------------------------------------------------------------------- */
    h1 {
      font-size: 16pt;
      color: gray;
    }

    pre {
      background-color: #eef2f7;
      padding-top: 10px;
      text-align: left;
      max-height: 600px;
      overflow: auto;
    }

    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
      background-color: #F5F5F5;
    }

    ::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      background-color: rgba(114, 124, 245, .25);
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
      background-color: #6c757d;
    }

    .heading {
      margin-top: 20px;
      margin-bottom: 1ex;
      margin-left: 10px;
      margin-right: 10px;
      width: 23%;
      float: left;
      padding-top: 10px;
      padding-left: 10px;
      padding-bottom: 10px;
      padding-right: 10px;
      box-shadow: 0px 0px 5px #000;
    }

    .heading .attribute {
      margin-top: 1ex;
      margin-bottom: 0;
    }

    .heading .description {
      margin-top: 4ex;
      margin-bottom: 6ex;
    }

    /* -- css div popup ------------------------------------------------------------------------ */
    a.popup_link {
      font-size: .8125rem;
    }

    a.popup_link:hover {
      background-color: #e7eaf0;
      font-size: .8125rem;
    }

    .log_window {
      max-width: 70%;
      max-height: 70%;
    }

    /* -- report ------------------------------------------------------------------------ */
    .show_detail_line {
      margin-left: 10px;
      margin-top: 30px;
      margin-bottom: 20px;
    }

    .show_detail_button {
      margin-top: 3ex;
      margin-bottom: 1ex;
      margin-left: 10px;
      text-align: right;
      margin-right: 15px;
    }

    .header_row {
      font-weight: bold;
      color: #606060;
      border-top-width: 10px;
      border-color: #d6e9c6;
      font-size: 15px;
    }

    .total_row {
      font-weight: bold;
      background-color: #dee2e6;
    }

    .passClass {
      background-color: #ccf5e7;
    }

    .failClass {
      background-color: #ffe8cc;
    }

    .errorClass {
      background-color: #ffd6e0;
    }

    .skipClass {
      background-color: #e7eaf0;
    }

    .passCase {
      color: #00CC88;
      font-weight: bold;
    }

    .failCase {
      color: #FF8C00;
      font-weight: bold;
    }

    .errorCase {
      color: #FF3366;
      font-weight: bold;
    }

    .skipCase {
      color: #525f7f;
      font-weight: bold;
    }

    .hiddenRow {
      display: none;
    }

    .caseStatistics {
      width: 46%
    }

    .none {
      color: #009900
    }

    .testcase {
      margin-left: 2em;
    }

    /* -- chars ---------------------------------------------------------------------- */
    .testChars {
      width: 900px;
      margin-left: 0px;
    }

    .error-color {
      color: #fff;
      background-color: #f44455;
      border-color: #f44455;
    }

    .pass-color {
      color: #fff;
      background-color: #5fc27e;
      border-color: #5fc27e;
    }

    .fail-color {
      color: #fff;
      background-color: #fcc100;
      border-color: #fcc100;
    }

    .skip-color {
      color: #fff;
      background-color: #6c757d;
      border-color: #6c757d;
    }

    /* -- screenshots ---------------------------------------------------------------------- */
    .img {
      border-collapse: collapse;
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      margin: auto;
    }

    .img-card {
      height: 600px;
      width: 800px;
      background-color: #e7eaf0;
    }

    .img-circle {
      height: 20px;
      border-radius: 12px;
      background-color: red;
      padding-left: 13px;
      margin: 0 auto;
      position: relative;
      top: -40px;
      background-color: rgba(1, 150, 0, 0.3);
    }

    .img-circle font {
      border: 1px solid white;
      width: 11px;
      height: 11px;
      border-radius: 50%;
      margin-right: 9px;
      margin-top: 4px;
      display: block;
      float: left;
      background-color: white;
    }

    .close-shots {
      position: absolute;
      top: 5px;
      right: 5px;
      z-index: 99;
    }

    .footer {
      height: 50px;
      width: 100%;
      position: fixed;
      bottom: 0;
    }

    #headContainer {
      margin-top: 20px;
      margin-bottom: 20px;
      padding-left: 30px;
      padding-right: 30px;
    }

    .head-list {
      padding-top: 5px;
      padding-bottom: 5px;
    }

    #resultContainer {
      margin-left: 30px;
      margin-right: 30px;
    }
  </style>
</head>

<body style="background-color: #f5f9fc">
  <nav
    class="navbar navbar-light position-lg-sticky top-lg-0 d-none d-lg-block overlap-10 flex-none bg-white border-bottom px-0 py-3"
    id="topbar">
    <div class="container-fluid">
      <div class="hstack gap-2">
        <a href="https://github.com/SeldomQA/XTestRunner">
          <img src="https://telegraph-image-cq2.pages.dev/XTestRunner_logo.jpg" style="height: 2.25rem;">
        </a>
      </div>
      <div class="navbar-user d-none d-sm-block">
        <div class="hstack gap-3 ms-4">
          <h3 style="float: right;"> 测试报告 </h3>
        </div>
      </div>
    </div>
  </nav>

  <div id="headContainer" class="container-fluid mm-active">
    <div class="row">
      <!-- Overview data -->
      <div class="col-12 col-lg-5 col-xl-3 d-flex" style="float:left">
        <div class='card flex-fill'>
          <div class="card-header">
            <div class="d-flex align-items-center">
              <h5 class="mb-0" id="overview">Overview</h5>
            </div>
          </div>
          <div class="card-body py-0 position-relative scrollable-y" style="max-height:300px">
            <div class="list-group list-group-flush">
              <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                <div class="flex-1">
                  <div class="d-flex align-items-center mb-1">👨‍🔧 Tester</div>
                  <div class="d-flex align-items-center">
                    <div class="text-sm text-muted line-clamp-1 me-auto">测试员</div>
                  </div>
                </div>
              </div>
              <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                <div class="flex-1">
                  <div class="d-flex align-items-center mb-1">🕒 Start time</div>
                  <div class="d-flex align-items-center">
                    <div class="text-sm text-muted line-clamp-1 me-auto">2025-05-13 08:32:49</div>
                  </div>
                </div>
              </div>
              <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                <div class="flex-1">
                  <div class="d-flex align-items-center mb-1">🕘 End time</div>
                  <div class="d-flex align-items-center">
                    <div class="text-sm text-muted line-clamp-1 me-auto">2025-05-13 08:32:49</div>
                  </div>
                </div>
              </div>
              <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                <div class="flex-1">
                  <div class="d-flex align-items-center mb-1">⌛ Duration</div>
                  <div class="d-flex align-items-center">
                    <div class="text-sm text-muted line-clamp-1 me-auto">0:00:00.001</div>
                  </div>
                </div>
              </div>
              <div class="list-group-item px-0 position-relative hstack flex-wrap" style="padding-top: 5px;">
                <div class="flex-1">
                  <div class="d-flex align-items-center mb-1">ℹ️ Description</div>
                  <div class="d-flex align-items-center">
                    <div class="text-sm text-muted me-auto">测试用例执行情况</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- passed & failed -->
      <div style="width: 20%">
        <div class="card" style="height: 45%;">
          <div class="card-body">
            <div class="row">
              <div class="col"><span class="h6 font-semibold text-muted text-sm d-block mb-2">Passed</span>
                <span class="h3 font-bold mb-0" id="p_number">1</span>
              </div>
              <div class="col-auto">
                <div class="icon icon-shape bg-success text-white text-lg rounded-circle">P</div>
              </div>
            </div>
            <div class="mt-2 mb-0 text-sm">
              <span class="badge badge-pill bg-soft-success text-success me-2">25.00%</span>
              <span class="text-nowrap text-xs text-muted">Pass rate</span>
            </div>
            <div class="d-flex align-items-center">
              <div class="progress" style="width:70%; margin-top: 5px;">
                <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                  aria-valuemax="100" style="width:25.00%"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="card" style="height: 45%; top: 10%;">
          <div class="card-body">
            <div class="row">
              <div class="col"><span class="h6 font-semibold text-muted text-sm d-block mb-2">Failure</span>
                <span class="h3 font-bold mb-0" id="f_number">1</span>
              </div>
              <div class="col-auto">
                <div class="icon icon-shape bg-warning text-white text-lg rounded-circle">F</div>
              </div>
            </div>
            <div class="mt-2 mb-0 text-sm">
              <span class="badge badge-pill bg-soft-warning text-warning me-2">25.00%</span>
              <span class="text-nowrap text-xs text-muted">Failure rate</span>
            </div>
            <div class="d-flex align-items-center">
              <div class="progress" style="width:70%; margin-top: 5px;">
                <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                  aria-valuemax="100" style="width:25.00%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- error & skipped -->
      <div style="width: 20%">
        <div class="card" style="height: 45%;">
          <div class="card-body">
            <div class="row">
              <div class="col"><span class="h6 font-semibold text-muted text-sm d-block mb-2">Errors</span>
                <span class="h3 font-bold mb-0" id="e_number">1</span>
              </div>
              <div class="col-auto">
                <div class="icon icon-shape bg-danger text-white text-lg rounded-circle">E</div>
              </div>
            </div>
            <div class="mt-2 mb-0 text-sm">
              <span class="badge badge-pill bg-soft-danger text-danger me-2">25.00%</span>
              <span class="text-nowrap text-xs text-muted">Error rate</span>
            </div>
            <div class="d-flex align-items-center">
              <div class="progress" style="width:70%; margin-top: 5px;">
                <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                  aria-valuemax="100" style="width:25.00%"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="card" style="height: 45%; top: 10%;">
          <div class="card-body">
            <div class="row">
              <div class="col">
                <span class="h6 font-semibold text-muted text-sm d-block mb-2">Skipped</span>
                <span class="h3 font-bold mb-0" id="s_number">1</span>
              </div>
              <div class="col-auto">
                <div class="icon icon-shape bg-secondary text-white text-lg rounded-circle">S</div>
              </div>
            </div>
            <div class="mt-2 mb-0 text-sm">
              <span class="badge badge-pill bg-soft-secondary text-secondary me-2">25.00%</span>
              <span class="text-nowrap text-xs text-muted">Skip rate</span>
            </div>
            <div class="d-flex align-items-center">
              <div class="progress" style="width:70%; margin-top: 5px;">
                <div class="progress-bar bg-secondary" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                  aria-valuemax="100" style="width:25.00%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- history data -->
      <div class="col-12 col-lg-5 col-xl-4 d-flex" style="float:right">
        <div class='card flex-fill'>
          <div id="echartElement" class="apex-charts" data-colors="#42d29d,#44badc" style="height:420px; padding:10px;">
          </div>
        </div>
      </div>
    </div>
  </div>
  <div id="resultContainer" class="card">
    <div class="card-header border-bottom">
      <span style="float: left">
        <h5 class="mb-0">Result</h5>
      </span>
      <span style="float: right">
        <a href="javascript:showCase(0, 1)" class="btn btn-dark btn-sm">Summary</a>
        <a href="javascript:showCase(1, 1)" class="btn btn-success btn-sm">Pass</a>
        <a href="javascript:showCase(2, 1)" class="btn btn-warning btn-sm">Failed</a>
        <a href="javascript:showCase(3, 1)" class="btn btn-danger btn-sm">Error</a>
        <a href="javascript:showCase(4, 1)" class="btn btn-secondary btn-sm">Skip</a>
        <a href="javascript:showCase(5, 1)" class="btn btn-info btn-sm">All</a>
      </span>
    </div>
    <div class="table-responsive">
      <table class="table table-hover table-nowrap">
        <thead class="table-light">
          <tr>
            <th scope="col" style="font-size: .875rem; font-weight: 800;">Test Group/Test Case</th>
            <th scope="col" style="font-size: .875rem; font-weight: 800;">Description</th>
            <th scope="col" style="font-size: .875rem; font-weight: 800;">Duration</th>
            <th scope="col" style="font-size: .875rem; font-weight: 800;">Result</th>
            <th scope="col" style="font-size: .875rem; font-weight: 800;">View</th>
            <th scope="col" style="font-size: .875rem; font-weight: 800;">Screenshots</th>
          </tr>
        </thead>
        <tbody>

          <tr class='passClass'>
            <td>testDemo</td>
            <td></td>
            <td></td>
            <td>Passed:1, Failure:1, Errors:1, Skipped:1</td>
            <td><a href="javascript:showClassDetail('c1.1',4)">Detail</a></td>
            <td>&nbsp;</td>
          </tr>

          <tr id='et1.1.1' class='none'>
            <td class='errorCase'>
              <div class='testcase'>test_error</div>
            </td>
            <td style="color: #495057">
              <div></div>
            </td>
            <td style="color: #495057">
              <div>0.00 s</div>
            </td>
            <td>
              <div class="progress" style="width:60px; height: 18px;">
                <div class='progress-bar bg-danger' role="progressbar" aria-valuenow="83" aria-valuemin="0"
                  aria-valuemax="100" style='width:100%'>Errors</div>
              </div>
            </td>
            <td>
              <!--css div popup start-->
              <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et1.1.1')">log</a>
              <div id='div_et1.1.1' class="modal show case-log" style="display: none; background-color: #000000c7;">
                <div class="modal-dialog modal-dialog-centered log_window">
                  <div class="modal-content shadow-3">
                    <div class="modal-header">
                      <div>
                        <h5 class="mb-1">test_error</h5>
                      </div>
                      <div>
                        <h5 class="mb-1">detailed log</h5>
                      </div>
                      <div>
                        <button type="button"
                          class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover"
                          data-bs-dismiss="modal" onclick="hideLog('div_et1.1.1')">X</button>
                      </div>
                    </div>
                    <div class="modal-body">
                      <div>
                        <pre>et1.1.1: Traceback (most recent call last):
  File "D:\软件测试\软件测试python\9\main.py", line 17, in test_error
    self.assertEqual(a, 6)  # type:ignore
                     ^
NameError: name 'a' is not defined
</pre>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--css div popup end-->
            </td>
            <td></td>
          </tr>

          <tr id='ft1.1.2' class='none'>
            <td class='failCase'>
              <div class='testcase'>test_fail</div>
            </td>
            <td style="color: #495057">
              <div></div>
            </td>
            <td style="color: #495057">
              <div>0.00 s</div>
            </td>
            <td>
              <div class="progress" style="width:60px; height: 18px;">
                <div class='progress-bar bg-warning' role="progressbar" aria-valuenow="83" aria-valuemin="0"
                  aria-valuemax="100" style='width:100%'>Failure</div>
              </div>
            </td>
            <td>
              <!--css div popup start-->
              <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft1.1.2')">log</a>
              <div id='div_ft1.1.2' class="modal show case-log" style="display: none; background-color: #000000c7;">
                <div class="modal-dialog modal-dialog-centered log_window">
                  <div class="modal-content shadow-3">
                    <div class="modal-header">
                      <div>
                        <h5 class="mb-1">test_fail</h5>
                      </div>
                      <div>
                        <h5 class="mb-1">detailed log</h5>
                      </div>
                      <div>
                        <button type="button"
                          class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover"
                          data-bs-dismiss="modal" onclick="hideLog('div_ft1.1.2')">X</button>
                      </div>
                    </div>
                    <div class="modal-body">
                      <div>
                        <pre>ft1.1.2: Traceback (most recent call last):
  File "D:\软件测试\软件测试python\9\main.py", line 14, in test_fail
    self.assertEqual(5, 6)
AssertionError: 5 != 6
</pre>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--css div popup end-->
            </td>
            <td></td>
          </tr>

          <tr id='st1.1.3' class='none'>
            <td class='skipCase'>
              <div class='testcase'>test_skip</div>
            </td>
            <td style="color: #495057">
              <div></div>
            </td>
            <td style="color: #495057">
              <div>0.00 s</div>
            </td>
            <td>
              <div class="progress" style="width:60px; height: 18px;">
                <div class='progress-bar bg-secondary' role="progressbar" aria-valuenow="83" aria-valuemin="0"
                  aria-valuemax="100" style='width:100%'>Skipped</div>
              </div>
            </td>
            <td>
              <!--css div popup start-->
              <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_st1.1.3')">log</a>
              <div id='div_st1.1.3' class="modal show case-log" style="display: none; background-color: #000000c7;">
                <div class="modal-dialog modal-dialog-centered log_window">
                  <div class="modal-content shadow-3">
                    <div class="modal-header">
                      <div>
                        <h5 class="mb-1">test_skip</h5>
                      </div>
                      <div>
                        <h5 class="mb-1">detailed log</h5>
                      </div>
                      <div>
                        <button type="button"
                          class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover"
                          data-bs-dismiss="modal" onclick="hideLog('div_st1.1.3')">X</button>
                      </div>
                    </div>
                    <div class="modal-body">
                      <div>
                        <pre>st1.1.3: 此用例跳过执行</pre>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--css div popup end-->
            </td>
            <td></td>
          </tr>

          <tr id='pt1.1.4' class='hiddenRow'>
            <td class='passCase'>
              <div class='testcase'>test_success</div>
            </td>
            <td style="color: #495057">
              <div></div>
            </td>
            <td style="color: #495057">
              <div>0.00 s</div>
            </td>
            <td>
              <div class="progress" style="width:60px; height: 18px;">
                <div class='progress-bar bg-success' role="progressbar" aria-valuenow="83" aria-valuemin="0"
                  aria-valuemax="100" style='width:100%'>Passed</div>
              </div>
            </td>
            <td></td>
            <td></td>
          </tr>

        </tbody>
      </table>
    </div>
    <div class="card-footer border-0 py-5">
      <span class="text-muted text-sm">
        Total:
        <button type="button" class="btn btn-sm bg-dark bg-opacity-20 bg-opacity-100-hover text-dark text-white-hover">
          4
        </button>
        =
        <button type="button"
          class="btn btn-sm bg-success bg-opacity-20 bg-opacity-100-hover text-success text-white-hover">
          1
        </button>
        +
        <button type="button"
          class="btn btn-sm bg-warning bg-opacity-20 bg-opacity-100-hover text-warning text-white-hover">
          1
        </button>
        +
        <button type="button"
          class="btn btn-sm bg-danger bg-opacity-20 bg-opacity-100-hover text-danger text-white-hover">
          1
        </button>
        +
        <button type="button"
          class="btn btn-sm bg-secondary bg-opacity-20 bg-opacity-100-hover text-secondary text-white-hover">
          1
        </button>
      </span>
    </div>
  </div>
  <div style="height: 120px"></div>
  <script language="javascript" type="text/javascript">
    // statistical data
    var myChart = echarts.init(document.getElementById("echartElement"));

    var overview = document.querySelector("#overview").textContent;
    var passed = document.querySelector("#p_number").textContent;
    var failure = document.querySelector("#f_number").textContent;
    var errors = document.querySelector("#e_number").textContent;
    var skipped = document.querySelector("#s_number").textContent;

    if (overview === "Overview") {
      var title = "Statistical data";
      var dataClass = ["Passed", "Failure", "Errors", "Skipped"];
    } else {
      var title = "统计数据";
      var dataClass = ["通过", "失败", "错误", "跳过"];
    }
    myChart.setOption({
      title: {
        text: title,
      },
      tooltip: {
        trigger: "axis",
      },
      xAxis: {
        type: "category",
        data: dataClass,
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          name: "Passed",
          type: "bar",
          stack: "Total",
          data: [
            {
              value: parseInt(passed),
              itemStyle: { color: "#00CC88" },
            },
            {
              value: parseInt(failure),
              itemStyle: { color: "#FF8C00" },
            },
            {
              value: parseInt(errors),
              itemStyle: { color: "#FF3366" },
            },
            {
              value: parseInt(skipped),
              itemStyle: { color: "#CFD6DF" },
            },
          ],
        },
      ],
    });

    function showLog(id) {
      document.body.style.overflow = "hidden";
      document.body.style.height = "100%";
      document.getElementById(id).style.display = "block";
    }

    function hideLog(id) {
      document.body.style.overflow = "auto";
      document.getElementById(id).style.display = "none";
    }

    function showImg(obj) {
      document.body.style.overflow = "hidden";
      document.body.style.height = "100%";
      var nextObj = obj.nextElementSibling;
      nextObj.style.display = "block";
      var index = 0; //每张图片的下标，
      var len = nextObj.getElementsByTagName("img").length;
      var imgCircle = nextObj.getElementsByClassName("img-circle")[0];
      nextObj.onmouseover = function () {
        //当鼠标光标停在图片上，则停止轮播
        clearInterval(start);
      };
      nextObj.onmouseout = function () {
        //当鼠标光标停在图片上，则开始轮播
        start = setInterval(autoPlay, 1000);
      };
      for (var i = 0; i < len; i++) {
        var fontTag = document.createElement("font");
        imgCircle.appendChild(fontTag);
      }
      var fontTagList = nextObj.getElementsByTagName("font"); //得到所有圆圈
      changeImg(0);
      var funny = function (i) {
        fontTagList[i].onmouseover = function () {
          index = i;
          changeImg(i);
        };
      };
      for (var i = 0; i < fontTagList.length; i++) {
        funny(i);
      }

      function autoPlay() {
        if (index > len - 1) {
          index = 0;
          clearInterval(start); //运行一轮后停止
        }
        changeImg(index++);
      }
      imgCircle.style.width = 30 * len + "px";
      // 对应圆圈和图片同步
      function changeImg(index) {
        var imgTags = nextObj.getElementsByTagName("img");
        var fontTags = nextObj.getElementsByTagName("font");
        for (i = 0; i < fontTags.length; i++) {
          imgTags[i].style.display = "none";
          fontTags[i].style.backgroundColor = "white";
        }
        imgTags[index].style.display = "block";
        if (fontTags.length > 0) {
          fontTags[index].style.backgroundColor = "red";
        }
      }
    }

    function hideImg(obj) {
      document.body.style.overflow = "auto";
      obj.parentElement.parentElement.parentElement.parentElement.parentElement.style.display =
        "none";
      obj.parentElement.parentElement.parentElement.getElementsByClassName(
        "img-circle"
      )[0].innerHTML = "";
    }

    document.addEventListener("keydown", function (e) {
      if (e.key === "Escape") {
        document.body.style.overflow = "auto";
        var casesLog = document.querySelectorAll(".case-log");
        var casesImage = document.querySelectorAll("#case-image");
        var imagCircle = document.querySelectorAll(".img-circle");
        for (var i = 0; i < casesLog.length; i++) {
          casesLog[i].style.display = "none";
        }
        for (var i = 0; i < casesImage.length; i++) {
          casesImage[i].style.display = "none";
        }
        for (var i = 0; i < imagCircle.length; i++) {
          imagCircle[i].innerHTML = "";
        }
      }
    });

    output_list = Array();
    /* level
    - 0:Summary
    - 1:Failed
    - 2:Skip
    - 3:All
    */
    function showCase(level, channel) {
      trs = document.getElementsByTagName("tr");
      for (var i = 0; i < trs.length; i++) {
        tr = trs[i];
        id = tr.id;
        if (["ft", "pt", "et", "st"].indexOf(id.substr(0, 2)) != -1) {
          if (level == 0 && id.substr(2, 1) == channel) {
            tr.className = "hiddenRow";
          }
        }
        if (id.substr(0, 3) == "pt" + channel) {
          if (level == 1) {
            tr.className = "";
          } else if (level > 4 && id.substr(2, 1) == channel) {
            tr.className = "";
          } else {
            tr.className = "hiddenRow";
          }
        }
        if (id.substr(0, 3) == "ft" + channel) {
          if (level == 2) {
            tr.className = "";
          } else if (level > 4 && id.substr(2, 1) == channel) {
            tr.className = "";
          } else {
            tr.className = "hiddenRow";
          }
        }
        if (id.substr(0, 3) == "et" + channel) {
          if (level == 3) {
            tr.className = "";
          } else if (level > 4 && id.substr(2, 1) == channel) {
            tr.className = "";
          } else {
            tr.className = "hiddenRow";
          }
        }
        if (id.substr(0, 3) == "st" + channel) {
          if (level == 4) {
            tr.className = "";
          } else if (level > 4 && id.substr(2, 1) == channel) {
            tr.className = "";
          } else {
            tr.className = "hiddenRow";
          }
        }
      }
    }
    function showClassDetail(cid, count) {
      var id_list = Array(count);
      var toHide = 1;
      for (var i = 0; i < count; i++) {
        tid0 = "t" + cid.substr(1) + "." + (i + 1);
        tid = "f" + tid0;
        tr = document.getElementById(tid);
        if (!tr) {
          tid = "p" + tid0;
          tr = document.getElementById(tid);
        }
        if (!tr) {
          tid = "e" + tid0;
          tr = document.getElementById(tid);
        }
        if (!tr) {
          tid = "s" + tid0;
          tr = document.getElementById(tid);
        }
        id_list[i] = tid;
        if (tr.className) {
          toHide = 0;
        }
      }
      for (var i = 0; i < count; i++) {
        tid = id_list[i];
        if (toHide) {
          document.getElementById(tid).className = "hiddenRow";
        } else {
          document.getElementById(tid).className = "";
        }
      }
    }
    function showTestDetail(div_id) {
      var detailsDiv = document.getElementById(div_id);
      var displayState = detailsDiv.style.display;

      if (displayState != "block") {
        displayState = "block";
        detailsDiv.style.display = "block";
      } else {
        detailsDiv.style.display = "none";
      }
    }
    function html_escape(s) {
      s = s.replace(/&/g, "&amp;");
      s = s.replace(/</g, "&lt;");
      s = s.replace(/>/g, "&gt;");
      return s;
    }
  </script>

  <footer class="footer" style="height: 50px; position: fixed; width: 100%">
    <div class="container-fluid">
      <div class="row">
        <div class="col-md-6">
          XTestRunner 1.8.4; 2025 © SeldomQA Team
        </div>
      </div>
    </div>
  </footer>
</body>

</html>