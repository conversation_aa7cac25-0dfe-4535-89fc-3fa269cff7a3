<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>iframe 内登录框</title>
    <script>
        function login() {
            const username = document.getElementById("username").value;
            const password = document.getElementById("password").value;
            const message = document.getElementById("message");

            if (username === "admin" && password === "123456") {
                message.style.color = "green";
                message.innerText = "登录成功！";
            } else {
                message.style.color = "red";
                message.innerText = "用户名或密码错误";
            }

            return false; // 阻止表单真正提交
        }
    </script>
</head>
<body>

    <h3>iframe 内的登录框</h3>

    <form onsubmit="return login();">
        用户名：<input type="text" id="username" name="username"><br><br>
        密码：<input type="password" id="password" name="password"><br><br>
        <button type="submit" id="login-btn">登录</button>
    </form>

    <p id="message"></p>

</body>
</html>
