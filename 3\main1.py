import select
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.support.ui import Select
import time
import os

HTML_FILENAME = "select_demo.html" 
print("正在设置 Chrome 驱动...")
try:
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service)
    print("驱动设置完成。")
except Exception as setup_error:
    print(f"驱动设置出错: {setup_error}")
    exit()
     
driver.implicitly_wait(10)

try:
    filepath_abs = os.path.abspath(HTML_FILENAME)
    if not os.path.exists(filepath_abs):
        print(f"错误：在路径 {filepath_abs} 未找到 HTML 文件")
        driver.quit()
        exit()
    filepath_url = "file:///" + filepath_abs
    print(f"正在加载页面: {filepath_url}")
    driver.get(filepath_url)
    print("页面加载完成")
except Exception as load_error:
    print(f"加载页面出错: {load_error}")
    driver.quit() 
    exit()

try:
    element=driver.find_element(By.ID,"college")
    button =driver.find_element(By.ID,"submit_value")
    select = Select(element)    
    #第一次选择:索引1
    select.select_by_index(1)
    button.click()
    result =driver.find_element(By.ID,"result")
    print("结果为",result.text)
    #第二次选择:文本"数字媒体学院"
    select.select_by_visible_text("数字媒体学院")
    button.click()
    result =driver.find_element(By.ID,"result")
    print("结果为",result.text)
except NoSuchElementException as e:
    print(f"元素未找到: {e}")
except Exception as e:
    print(e)
finally:
    time.sleep(5)
    driver.quit()