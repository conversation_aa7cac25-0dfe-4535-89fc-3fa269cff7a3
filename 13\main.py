#!/usr/bin/env -S python -v

# 使用selenium编写前端自动化测试脚本,访问网易邮箱,设置智能时间等待3秒,浏览器窗口自动化

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from time import sleep

service = Service(ChromeDriverManager().install())
driver = webdriver.Chrome(service=service)
driver.get("https://mail.126.com/")
driver.maximize_window()
frame = driver.find_element(By.XPATH, "//iframe[contains(@id,'URS-iframe')]")
driver.switch_to(frame)
driver.find_element(By.NAME, "email").send_keys("zhangsan")
driver.find_element(By.NAME, "password").send_keys("123456")
driver.find_element(By.NAME, "dologin").click()
driver.get_screenshot_as_file("main.png")
driver.switch_to.default_content()
mainhandle = driver.current_window_handle  # 当前页面的句柄
driver.find_element(By.LINK_TEXT, "帮助").click()
allhandle = driver.window_handles
helphandle = next(h for h in allhandle if h != mainhandle)
driver.switch_to.window(helphandle)
driver.find_element(By.CLASS_NAME,"searchIconInner").send_keys("如何重置密码")
time(3)


